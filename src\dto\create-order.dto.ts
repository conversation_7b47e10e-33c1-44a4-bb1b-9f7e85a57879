import { Rule, RuleType } from '@midwayjs/validate';
import { DiscountType } from '../interface';

/**
 * 创建订单DTO
 */
export class CreateOrderDto {
  @Rule(RuleType.number().integer().required().error(new Error('客户ID不能为空')))
  /** 客户ID */
  customerId: number;

  @Rule(RuleType.number().integer().optional().error(new Error('员工ID必须是整数')))
  /** 员工ID */
  employeeId?: number;

  @Rule(RuleType.date().required().error(new Error('下单时间不能为空')))
  /** 下单时间 */
  orderTime: Date;

  @Rule(RuleType.date().optional().allow(null).error(new Error('预约服务时间格式错误')))
  /** 预约服务时间 */
  serviceTime: Date | null;

  @Rule(RuleType.number().integer().optional().error(new Error('地址ID必须是整数')))
  /** 地址ID */
  addressId?: number;

  @Rule(RuleType.string().required().max(255).error(new Error('服务地址不能为空且不能超过255字符')))
  /** 服务地址 */
  address: string;

  @Rule(RuleType.number().required().min(-180).max(180).error(new Error('经度必须在-180到180之间')))
  /** 服务地址经度 */
  longitude: number;

  @Rule(RuleType.number().required().min(-90).max(90).error(new Error('纬度必须在-90到90之间')))
  /** 服务地址纬度 */
  latitude: number;

  @Rule(RuleType.string().required().max(255).error(new Error('服务地址详情不能为空且不能超过255字符')))
  /** 服务地址详情 */
  addressDetail: string;

  @Rule(RuleType.string().optional().max(255).error(new Error('服务地址备注不能超过255字符')))
  /** 服务地址备注 */
  addressRemark?: string;

  @Rule(RuleType.number().required().min(0).error(new Error('订单原价不能为负数')))
  /** 订单原价 */
  originalPrice: number;

  @Rule(RuleType.number().required().min(0).error(new Error('订单总费用不能为负数')))
  /** 订单总费用 */
  totalFee: number;

  @Rule(RuleType.number().optional().min(0).default(0).error(new Error('权益卡抵扣金额不能为负数')))
  /** 权益卡抵扣金额 */
  cardDeduction: number;

  @Rule(RuleType.number().optional().min(0).default(0).error(new Error('代金券抵扣金额不能为负数')))
  /** 代金券抵扣金额 */
  couponDeduction: number;

  @Rule(RuleType.string().optional().max(500).error(new Error('订单备注不能超过500字符')))
  /** 订单备注 */
  orderRemark?: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string().uri())
      .max(3)
      .optional()
      .error(new Error('说明图片最多3张，且必须是有效的URL'))
  )
  /** 说明图片URL数组，最多3张 */
  remarkPhotos?: string[];

  @Rule(RuleType.array().required().min(1).error(new Error('订单明细不能为空')))
  /** 订单明细列表 */
  orderDetails: CreateOrderDetailDto[];

  @Rule(RuleType.array().optional().error(new Error('优惠信息格式错误')))
  /** 优惠信息列表 */
  discountInfos?: Array<{
    discountType: DiscountType;
    discountId: number;
    discountAmount: number;
  }>;
}

/**
 * 创建订单明细DTO
 */
export class CreateOrderDetailDto {
  @Rule(RuleType.number().integer().required().error(new Error('服务ID不能为空')))
  /** 服务ID */
  serviceId: number;

  @Rule(RuleType.string().required().max(100).error(new Error('服务名称不能为空且不能超过100字符')))
  /** 服务名称 */
  serviceName: string;

  @Rule(RuleType.number().required().min(0).error(new Error('服务价格不能为负数')))
  /** 服务基础价格 */
  servicePrice: number;

  @Rule(RuleType.number().integer().required().error(new Error('宠物ID不能为空')))
  /** 宠物ID */
  petId: number;

  @Rule(RuleType.string().required().max(50).error(new Error('宠物名称不能为空且不能超过50字符')))
  /** 宠物名称 */
  petName: string;

  @Rule(RuleType.string().required().max(50).error(new Error('宠物类型不能为空且不能超过50字符')))
  /** 宠物类型 */
  petType: string;

  @Rule(RuleType.string().required().max(50).error(new Error('宠物品种不能为空且不能超过50字符')))
  /** 宠物品种 */
  petBreed: string;

  @Rule(RuleType.date().required().error(new Error('下单时间不能为空')))
  /** 下单时间 */
  orderTime: Date;

  @Rule(RuleType.string().optional().max(255).error(new Error('用户备注不能超过255字符')))
  /** 用户备注 */
  userRemark?: string;

  @Rule(RuleType.array().optional().error(new Error('增项服务ID列表格式错误')))
  /** 增项服务ID列表 */
  addServiceIds: number[][];
}
