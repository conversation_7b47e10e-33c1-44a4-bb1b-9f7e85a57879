# 订单业务规则

## 订单状态流转

### 基本状态流转
```
待付款 → 待接单 → 待服务 → 已出发 → 服务中 → 已完成 → 已评价
```

### 异常状态流转
```
待付款 → 已取消
待接单 → 已取消
待服务 → 退款中 → 已退款
已出发 → 退款中 → 已退款
```

### 状态说明
- **待付款**: 订单创建后，等待用户付款
- **待接单**: 付款完成后，等待员工接单
- **待服务**: 员工接单后，等待上门服务
- **已出发**: 员工出发前往服务地点
- **服务中**: 员工开始提供服务
- **已完成**: 服务完成，等待用户评价
- **已评价**: 用户完成评价，订单流程结束
- **已取消**: 订单被取消
- **退款中**: 申请退款，等待审核
- **已退款**: 退款完成

## 订单创建规则

### 基本信息验证
1. **客户信息**: 必须提供有效的客户ID
2. **服务信息**: 必须选择至少一个服务项目
3. **宠物信息**: 每个服务项目必须关联一个宠物
4. **服务时间**: 必须选择未来的服务时间
5. **服务地址**: 必须提供完整的服务地址
   - 服务地址（必填，最多255字符）
   - 服务地址详情（必填，最多255字符）
   - 经纬度坐标（必填，经度-180到180，纬度-90到90）
   - 地址备注（可选，最多255字符）
6. **订单备注**: 可选，最多500字符
7. **说明图片**: 可选，最多3张图片

### 价格计算规则
1. **原价计算**: 所有服务项目价格之和
2. **权益卡抵扣**: 根据权益卡类型计算折扣
3. **代金券抵扣**: 满足使用条件的代金券金额
4. **最终价格**: 原价 - 权益卡抵扣 - 代金券抵扣

### 0元订单处理
- 当最终价格为0时，订单自动跳过付款状态
- 直接从"待付款"变为"待接单"状态
- 系统自动标记为已付款

## 接单规则

### 员工接单权限
1. **职位限制**: 
   - 洗护师只能接洗护类订单
   - 美容师可以接洗护和美容类订单
2. **状态检查**: 只有"待接单"状态的订单可以被接单
3. **重复接单**: 同一订单不能被多个员工同时接单

### 指定员工订单
1. **专属接单**: 指定员工的订单只有该员工可以看到并接单
2. **转单机制**: 管理员可以将订单转给其他员工
3. **拒绝接单**: 员工可以拒绝接单，订单重新进入待接单状态

### 自动派单规则
1. **距离优先**: 优先派给距离最近的员工
2. **工作量平衡**: 考虑员工当前工作量
3. **专业匹配**: 根据服务类型匹配员工职位

## 服务执行规则

### 服务开始
1. **状态更新**: 从"已出发"或"待服务"更新为"服务中"
2. **自动计时**: 自动开始所有主服务项目的计时
3. **照片上传**: 可选择上传服务前照片

### 服务计时规则
1. **主服务**: 开始整体服务时自动开始计时
2. **增项服务**: 需要员工手动开始计时
3. **并发服务**: 允许同时进行多个服务项目
4. **时长统计**: 取最近10次记录的平均值更新到服务表

### 服务完成条件
1. **主服务完成**: 所有主服务项目都已结束
2. **增项服务完成**: 所有增项服务都已结束
3. **无未付费追加**: 没有未付费的追加服务
4. **无未开始追加**: 没有未开始的追加服务

## 追加服务规则

### 申请条件
1. **订单状态**: 订单必须处于"服务中"状态
2. **服务选择**: 可以选择系统中的增项服务
3. **数量限制**: 每个增项服务可以选择数量

### 确认流程
1. **员工确认**: 负责该订单的员工进行确认或拒绝
2. **用户付款**: 员工确认后用户进行付款
3. **服务执行**: 付款完成后可以开始追加服务

### 价格计算
- 实际支付金额 = 原价 - 权益卡抵扣 - 代金券抵扣
- 支持使用权益卡和代金券

## 退款规则

### 退款条件
1. **状态限制**: 只有"待服务"或"已出发"状态的订单可以申请退款
2. **时间限制**: 服务开始后不能申请退款
3. **管理员权限**: 管理员可以对任何状态的订单进行退款

### 退款流程
1. **用户申请**: 用户提交退款申请
2. **状态变更**: 订单状态变为"退款中"
3. **管理审核**: 管理员审核退款申请
4. **退款处理**: 审核通过后进行退款操作
5. **状态更新**: 退款完成后状态变为"已退款"

### 退款金额计算
1. **全额退款**: 退还用户实际支付的全部金额
2. **部分退款**: 根据服务完成情况计算退款金额
3. **优惠券处理**: 全额退款时退还优惠券，部分退款时不退还

### 追加服务退款
1. **已付费追加服务**: 删除时必须实施退款功能
2. **退款检查**: 管理员删除订单时检查支付信息并退款
3. **数据一致性**: 防止数据不一致问题

## 订单地址修改规则

### 修改权限
1. **管理员**: 任何状态的订单都可以修改地址
2. **用户和员工**: 只能在派单前（"已出发"状态前）修改地址
3. **状态检查**: 系统自动检查订单状态决定是否允许修改

### 修改限制
1. **服务开始后**: 服务开始后不允许修改地址
2. **员工出发后**: 员工出发后原则上不允许修改
3. **特殊情况**: 管理员可以强制修改并通知相关人员

## 订单删除规则

### 删除权限
1. **管理员专属**: 只有管理员可以删除订单
2. **最大权限**: 管理员可以删除任何状态的订单

### 删除前检查
1. **支付信息检查**: 检查主订单和追加服务的支付信息
2. **自动退款**: 有支付记录的订单删除前自动退款
3. **数据一致性**: 确保删除操作不会导致数据不一致

### 关联数据处理
1. **微信消息订阅**: 删除订单时清除相关的微信消息订阅
2. **服务记录**: 保留服务时长记录用于统计
3. **操作日志**: 保留订单操作日志

## 特殊场景处理

### 支付异常处理
1. **微信支付成功但本地更新失败**: 
   - 提供手动同步接口
   - 防止重复支付
   - 记录异常日志
2. **重复退款防护**: 
   - 检查退款状态
   - 防止重复退款操作
   - 记录退款历史

### 并发处理
1. **接单竞争**: 使用数据库锁防止重复接单
2. **状态更新**: 确保状态更新的原子性
3. **库存扣减**: 处理权益卡和代金券的并发使用

### 数据恢复
1. **订单状态恢复**: 提供状态回滚机制
2. **支付状态同步**: 定期同步微信支付状态
3. **数据修复**: 提供数据修复工具

## 性能优化规则

### 查询优化
1. **冗余字段**: 在订单详情中保存服务名称等信息，避免复杂JOIN
2. **索引优化**: 在常用查询字段上建立索引
3. **分页查询**: 大数据量查询必须使用分页

### 缓存策略
1. **热点数据**: 缓存常用的服务信息和价格
2. **用户会话**: 缓存用户登录状态和权限信息
3. **计算结果**: 缓存复杂的价格计算结果

### 数据库事务
1. **复杂操作**: 订单创建、退款等复杂操作使用事务
2. **外部调用**: 微信支付等外部调用的异常处理
3. **数据一致性**: 确保关联数据的一致性

## 监控和告警

### 业务监控
1. **订单量监控**: 监控订单创建和完成量
2. **异常订单**: 监控长时间未处理的订单
3. **退款率**: 监控退款率异常

### 系统监控
1. **接口性能**: 监控关键接口的响应时间
2. **错误率**: 监控接口错误率
3. **数据库性能**: 监控数据库查询性能

### 告警机制
1. **实时告警**: 关键异常实时告警
2. **定期报告**: 定期生成业务报告
3. **趋势分析**: 分析业务数据趋势
